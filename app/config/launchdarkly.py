import ldclient

from app.common.logger import logger
from app.settings import settings


def configure_launchdarkly():
    """Initialize LaunchDarkly client with SDK key from settings."""
    if settings.LAUNCH_DARKLY_SDK_KEY:
        ldclient.set_config(ldclient.Config(settings.LAUNCH_DARKLY_SDK_KEY))
        logger.info("LaunchDarkly initialized successfully")
    else:
        logger.warning("LaunchDarkly SDK key not configured - feature flags will default to False")


def cleanup_launchdarkly():
    """Close LaunchDarkly client connection."""
    if settings.LAUNCH_DARKLY_SDK_KEY:
        ldclient.get().close()
        logger.info("LaunchDarkly client closed")
