import asyncio

import grpc
from cloudbeds.occupancy.v1 import occupancy_pb2, occupancy_pb2_grpc
from ddtrace import auto as auto
from ddtrace import tracer
from grpc_health.v1 import health_pb2, health_pb2_grpc
from grpc_health.v1._async import HealthServicer
from grpc_interceptor import AsyncExceptionToStatusInterceptor
from grpc_reflection.v1alpha import reflection

from app.common.logger import GrpcJ<PERSON>NF<PERSON>atter, configure_logger, logger
from app.ddtrace.filters import FilterRequestsOnGrpcMethod
from app.grpc.interceptor.logger_interceptor import LoggerInterceptor
from app.grpc.servicer.occupancy import OccupancyServiceServicer

# Coroutines to be invoked when the event loop is shutting down.
_cleanup_coroutines = []


async def serve() -> None:
    tracer.configure(
        trace_processors=[FilterRequestsOnGrpcMethod(["/grpc.health.v1.Health/Check"])]
    )
    configure_logger(GrpcJSONFormatter())
    server = grpc.aio.server(
        interceptors=(
            LoggerInterceptor(),
            AsyncExceptionToStatusInterceptor(),
        ),
    )

    occupancy_pb2_grpc.add_OccupancyServiceServicer_to_server(
        OccupancyServiceServicer(), server
    )
    health_pb2_grpc.add_HealthServicer_to_server(HealthServicer(), server)
    SERVICE_NAMES = (
        occupancy_pb2.DESCRIPTOR.services_by_name["OccupancyService"].full_name,
        health_pb2.DESCRIPTOR.services_by_name["Health"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(SERVICE_NAMES, server)

    port = "[::]:50051"
    server.add_insecure_port(port)
    logger.info(f"gRPC server started on port {port}")
    await server.start()

    async def server_graceful_shutdown():
        logger.info("Starting graceful shutdown...")
        # Shuts down the server with 5 seconds of grace period. During the
        # grace period, the server won't accept new connections and allow
        # existing RPCs to continue within the grace period.
        await server.stop(5)

    logger.info("Waiting for term")
    _cleanup_coroutines.append(server_graceful_shutdown())
    await server.wait_for_termination()


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(serve())
    finally:
        loop.run_until_complete(*_cleanup_coroutines)
        loop.close()
