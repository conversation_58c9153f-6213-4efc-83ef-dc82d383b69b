import types
import uuid

from ddtrace import auto as auto
from ddtrace.trace import tracer
from fastapi import FastAPI, HTTPException, Request
from fastapi.concurrency import asynccontextmanager
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi_babel import BabelMiddleware
from pydantic import ValidationError
from sqlalchemy.exc import OperationalError, ProgrammingError

import app.common.context_variables as global_vars
from app.common.babel import babel_configs
from app.common.constants import API_VERSION, BASE_URL, X_ORGANIZATION_ID, X_PROPERTY_ID
from app.common.exceptions import (
    general_errorhandler,
    http_exception_handler,
    request_validation_errorhandler,
    sqlalchemy_errorhandler,
    validation_errorhandler,
)
from app.common.logger import FastApiJSONFormatter, configure_logger, logger
from app.ddtrace.filters import FilterRequestsOnUrl
from app.middleware.logging import LogMiddleware
from app.routers.health import router as health_router
from app.routers.query import router as query_router
from app.services.token import TokenService


@asynccontextmanager
async def lifespan(app: FastAPI):
    configure_logger(FastApiJSONFormatter())
    logger.info("Starting Occupancy Service App...")
    yield


app = FastAPI(
    title="Occupancy Service",
    description="This service provides information about occupancy for a given property or organization.",
    version=f"{API_VERSION}",
    docs_url="/occupancy",
    openapi_url="/occupancy/openapi.json",
    lifespan=lifespan,
)

tracer.configure(
    trace_processors=[
        FilterRequestsOnUrl(
            [r"http://.*/occupancy/v1/health", r"http://.*/occupancy/openapi.json"]
        )
    ]
)


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exception_data: Exception):
    payload, status_code = general_errorhandler(exception_data)
    return JSONResponse(
        status_code=status_code,
        content=payload,
    )


@app.exception_handler(OperationalError)
async def sqlalchemy_operational_error_handler(
    request: Request, exception_data: OperationalError
):
    payload, status_code = sqlalchemy_errorhandler(exception_data)
    return JSONResponse(
        status_code=status_code,
        content=payload,
    )


@app.exception_handler(ProgrammingError)
async def sqlalchemy_programming_error_handler(
    request: Request, exception_data: ProgrammingError
):
    payload, status_code = sqlalchemy_errorhandler(exception_data)
    return JSONResponse(
        status_code=status_code,
        content=payload,
    )


@app.exception_handler(ValidationError)
async def pydantic_validation_error_handler(
    request: Request, exception_data: ValidationError
):
    payload, status_code = validation_errorhandler(exception_data)
    return JSONResponse(
        status_code=status_code,
        content=payload,
    )


@app.exception_handler(RequestValidationError)
async def pydantic_value_error_handler(
    request: Request, exception_data: RequestValidationError
):
    payload, status_code = request_validation_errorhandler(exception_data)
    return JSONResponse(
        status_code=status_code,
        content=payload,
    )


@app.exception_handler(HTTPException)
async def pydantic_http_exception_handler(
    request: Request, exception_data: HTTPException
):
    payload, status_code = http_exception_handler(exception_data)
    return JSONResponse(
        status_code=status_code,
        content=payload,
    )


@app.middleware("http")
async def init_requestvars(request: Request, call_next):
    # Customize that SimpleNamespace with whatever you need
    initial_g = types.SimpleNamespace()
    initial_g.request_id = str(uuid.uuid4())
    initial_g.origin = request.headers.get("origin", "-")
    initial_g.x_amz_trace_id = request.headers.get("x-amzn-trace-id", "-")
    initial_g.property_id = (
        int(request.headers.get(X_PROPERTY_ID))
        if request.headers.get(X_PROPERTY_ID)
        else None
    )
    initial_g.organization_id = (
        int(request.headers.get(X_ORGANIZATION_ID))
        if request.headers.get(X_ORGANIZATION_ID)
        else None
    )
    initial_g.endpoint = (
        f"{request.method} -> {request.url.path}?{request.query_params}"
    )
    access_token = request.headers.get("authorization")
    if access_token:
        token = TokenService(access_token)
        initial_g.user_email = token.get_email()
        initial_g.user_id = token.get_user_id()
    global_vars.request_global.set(initial_g)

    response = await call_next(request)
    return response


app.add_middleware(LogMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=("*",),
    allow_headers=("*",),
    allow_credentials=True,
    max_age=3600,
)
app.add_middleware(BabelMiddleware, babel_configs=babel_configs)

app.include_router(health_router, prefix=BASE_URL)
app.include_router(query_router, prefix=BASE_URL)
