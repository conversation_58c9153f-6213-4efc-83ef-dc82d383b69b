import ldclient
from ldclient import Context

from app.common.cache import TIMEOUT_MINUTE, cache
from app.common.enums.features import LaunchDarklyFeature
from app.common.logger import logger


class LaunchDarklyService:
    @staticmethod
    @cache.memoize(TIMEOUT_MINUTE)
    def has_feature_flag(key: LaunchDarklyFeature, organization_id: int) -> bool:
        """Check if a feature flag is enabled for a specific organization."""
        try:
            return ldclient.get().variation(
                key.value, Context.builder(f"association:{organization_id}").build(), False
            )
        except Exception as error:
            logger.error(
                "There was a problem with Launch Darkly",
                extra={
                    "error": str(error),
                    "feature_flag": key.value,
                    "organization_id": organization_id,
                },
            )
            return False
