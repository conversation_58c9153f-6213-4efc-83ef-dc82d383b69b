import ldclient
from ldclient import Context

from app.common.cache import TIMEOUT_MINUTE, cache
from app.common.enums.features import LaunchDarklyFeature
from app.common.logger import logger


class LaunchDarklyService:
    @staticmethod
    @cache.memoize(TIMEOUT_MINUTE)
    def has_feature_flag(key: LaunchDarklyFeature, property_id: int) -> bool:
        """
        Check if a feature flag is enabled for a specific property.

        Note: When QueryService receives multiple property_ids, if ANY property
        has the flag enabled, then optimization is enabled for ALL properties
        in that request. This simplifies the logic and avoids complex per-property
        query building.
        """
        try:
            return ldclient.get().variation(
                key.value, Context.builder(f"property:{property_id}").build(), False
            )
        except Exception as error:
            logger.error(
                "There was a problem with Launch Darkly",
                extra={
                    "error": str(error),
                    "feature_flag": key.value,
                    "property_id": property_id,
                },
            )
            return False
