import time
import traceback
from datetime import date
from enum import Enum

import pandas as pd
from fastapi_babel import _
from sqlalchemy import (
    BigInteger,
    Date,
    Float,
    Integer,
    String,
    cast,
    func,
    literal_column,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import class_mapper

from app.common.cache import cache
from app.common.enums.features import LaunchDarklyFeature
from app.common.exceptions import InvalidUsage
from app.common.logger import logger
from app.enums.column import Column, get_model_columns
from app.enums.granularity import Granularity
from app.enums.group_by import GroupBy
from app.enums.sort import Sort
from app.models.occupancy import Occupancy, OccupancyAndRevenue
from app.services.launch_darkly import LaunchDarklyService
from app.services.query_builders import OptimizedQuery, TraditionalQuery


class QueryService:
    def __init__(
        self,
        database: AsyncSession,
        property_ids: list[int],
        organization_id: int,
        cast_results_to_string: bool = False,
    ):
        self.database = database
        self.model = OccupancyAndRevenue
        self.property_ids = property_ids
        self.organization_id = organization_id
        self.cast_results_to_string = cast_results_to_string

    @cache.memoize(ex=60)
    async def get(
        self,
        columns: list[Column],
        group_by: list[GroupBy],
        start_date: date,
        end_date: date,
        granularity: Granularity,
        offset: int,
        limit: int,
        room_type_ids: list[int] = None,
        sort: list[Sort] = None,
    ) -> tuple | InvalidUsage:
        """
        Main method for querying occupancy data.
        """
        try:
            column_names = self.__extract_column_names(columns)
            group_by_names = self.__extract_column_names(group_by)
            self.model = self.__determine_model(column_names, group_by_names, sort)

            selected_columns, group_by_expressions = self.__prepare_group_by_columns(
                group_by_names, granularity, column_names
            )
            aggregation_columns = self.__generate_aggregation_columns()
            sort_columns = self.__generate_sort_columns(sort, group_by_names)
            selected_columns += [
                aggregation_columns[column]
                for column in column_names
                if column in aggregation_columns
            ]

            if self.__should_use_optimized_queries():
                query_builder = OptimizedQuery(self.property_ids, self.organization_id)
            else:
                query_builder = TraditionalQuery(
                    self.property_ids, self.organization_id, self.model
                )

            query = (
                query_builder.build(
                    selected_columns,
                    group_by_expressions,
                    start_date,
                    end_date,
                    room_type_ids,
                    sort_columns,
                )
                .offset(offset)
                .limit(limit)
            )

            result = await self.__execute_query(query, selected_columns)
            result_df = pd.DataFrame(result)
            data = self.__process_results(result_df, group_by_names)

            return data, limit, offset, sort

        except SQLAlchemyError as db_error:
            logger.error(f"Database Error: {str(db_error)}")
            raise InvalidUsage.server_error(
                message=f"Database error: {str(db_error)}"
            ) from db_error

        except Exception as exception:
            logger.error(f"Unexpected Error: {str(exception)}")
            logger.error(traceback.format_exc())
            raise InvalidUsage.server_error(
                message="An unexpected error occurred. Please check the logs."
            ) from exception

    def __determine_model(
        self, columns: list[str], group_bys: list[str] = None, sorts: list[Sort] = None
    ) -> type:
        if not columns and not group_bys and not sorts:
            return Occupancy

        has_revenue = False
        occupancy_only_columns = get_model_columns(Occupancy)
        for column in columns:
            if column not in occupancy_only_columns:
                has_revenue = True
                logger.info(f"column={column} not in {occupancy_only_columns}")
                break

        for group_by in group_bys:
            if group_by not in occupancy_only_columns:
                has_revenue = True
                logger.info(f"group_by={group_by} not in {occupancy_only_columns}")
                break

        if sorts:
            for sort in sorts:
                sort_field, _ = sort.value.split(":")
                if sort_field not in occupancy_only_columns:
                    has_revenue = True
                    logger.info(f"sort={sort_field} not in {occupancy_only_columns}")
                    break

        if has_revenue:
            return OccupancyAndRevenue

        return Occupancy

    def __extract_column_names(self, columns: list[Enum]) -> list[str]:
        return [
            column.value if isinstance(column, Enum) else column for column in columns
        ]

    def __prepare_group_by_columns(
        self, group_by_columns: list[str], granularity: Granularity, columns: list[str]
    ):
        selected_columns = []
        group_by_expressions = []
        groups_set = set(group_by_columns)

        for group in group_by_columns:
            if self.__should_use_optimized_queries():
                non_aggregated_columns = self.__generate_non_aggregation_columns()
                if group in non_aggregated_columns:
                    column_attr = non_aggregated_columns[group]
                    selected_columns.append(column_attr)
                    group_by_expressions.append(column_attr)
                else:
                    column_attr = getattr(self.model, group)
                    if isinstance(column_attr.type, Date):
                        casted_column = self.__cast_dates_cdf(column_attr, granularity)
                        selected_columns.append(casted_column)
                        group_by_expressions.append(casted_column)
                    else:
                        selected_columns.append(column_attr)
                        group_by_expressions.append(column_attr)
            else:
                column_attr = getattr(self.model, group)
                if isinstance(column_attr.type, Date):
                    casted_column = self.__cast_dates_cdf(column_attr, granularity)
                    selected_columns.append(casted_column)
                    group_by_expressions.append(casted_column)
                else:
                    selected_columns.append(column_attr)
                    group_by_expressions.append(column_attr)

        non_aggregated_columns = self.__generate_non_aggregation_columns()
        for column in columns:
            if column in non_aggregated_columns and column not in groups_set:
                column_attr = non_aggregated_columns[column]
                selected_columns.append(column_attr)

                if not self.__should_use_optimized_queries():
                    group_by_expressions.append(column_attr)

        return selected_columns, group_by_expressions

    def __generate_aggregation_columns(self) -> dict:
        if self.__should_use_optimized_queries():
            return self.__generate_optimized_aggregation_columns()
        else:
            return {
                column.key: cast(
                    func.coalesce(
                        func.sum(column)
                        if column.key
                        not in self.model.__excluded_default_aggregate_columns__
                        else column,
                        0,
                    ),
                    Float if isinstance(column.type, Float) else Integer,
                ).label(column.key)
                for column in class_mapper(self.model).columns
                if isinstance(column.type, (Integer, BigInteger, Float))
                and column.key in self.model.__aggregate_columns__
            }

    def __generate_optimized_aggregation_columns(self) -> dict:
        return {
            "occupancy": literal_column("occupancy_placeholder").label("occupancy"),
            "capacity_count": literal_column("capacity_count_placeholder").label(
                "capacity_count"
            ),
            "total_rooms_sold": literal_column("total_rooms_sold_placeholder").label(
                "total_rooms_sold"
            ),
            "total_rooms_available": literal_column(
                "total_rooms_available_placeholder"
            ).label("total_rooms_available"),
            "blocked_rooms_count": literal_column(
                "blocked_rooms_count_placeholder"
            ).label("blocked_rooms_count"),
            "out_of_service_count": literal_column(
                "out_of_service_count_placeholder"
            ).label("out_of_service_count"),
            "guest_count": literal_column("guest_count_placeholder").label(
                "guest_count"
            ),
            "adults_count": literal_column("adults_count_placeholder").label(
                "adults_count"
            ),
            "children_count": literal_column("children_count_placeholder").label(
                "children_count"
            ),
        }

    def __generate_non_aggregation_columns(self) -> dict:
        """
        Generate column expressions for those columns that should not be aggregated.
        Handles both traditional and optimized approaches.
        """
        if self.__should_use_optimized_queries():
            return self.__generate_optimized_non_aggregation_columns()
        else:
            return {
                column.key: column.label(column.key)
                for column in class_mapper(self.model).columns
                if column.key not in self.model.__aggregate_columns__
            }

    def __generate_optimized_non_aggregation_columns(self) -> dict:
        return {
            "stay_date": literal_column("stay_date_placeholder").label("stay_date"),
            "organization_id": literal_column("organization_id_placeholder").label(
                "organization_id"
            ),
            "property_id": literal_column("property_id_placeholder").label(
                "property_id"
            ),
            "room_type_id": literal_column("room_type_id_placeholder").label(
                "room_type_id"
            ),
            "room_type": literal_column("room_type_placeholder").label("room_type"),
            "group_profile_code": literal_column(
                "group_profile_code_placeholder"
            ).label("group_profile_code"),
        }

    def __generate_sort_columns(self, sort: list[Sort], groups: list[str]) -> list:
        """
        Converts Sort enums into SQLAlchemy order_by expressions.
        If no sort is defined, fallback to ordering by group-by columns to maintain consistency.
        """
        sorts = []

        if not sort:
            for group in groups:
                sorts.append(literal_column(group).asc().nullsfirst())

            return sorts

        for __sort in sort:
            field, direction = __sort.value.split(":")
            column = literal_column(field)
            sorts.append(column.asc() if direction == "asc" else column.desc())

        return sorts

    def __should_use_optimized_queries(self) -> bool:
        if self.model != Occupancy:
            return False

        # Check LaunchDarkly feature flag for each property
        # If ANY property has the flag enabled, enable optimization for ALL properties
        # This simplifies query building logic and avoids complex per-property handling
        for property_id in self.property_ids:
            if LaunchDarklyService.has_feature_flag(
                LaunchDarklyFeature.OptimizedQueries, property_id
            ):
                return True

        return False

    async def __execute_query(self, query, selected_columns):
        """
        Executes the query and returns the results as a Pandas DataFrame.
        """
        compiled_query = query.compile(
            dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}
        )

        logger.info(f"Query to be executed: {compiled_query}")

        start_time = time.time()
        result = await self.database.execute(query)
        rows = result.all()
        execution_time = time.time() - start_time

        logger.info(f"Query executed in {execution_time:.4f} seconds")

        data = [
            dict(zip([column.name for column in selected_columns], row, strict=False))
            for row in rows
        ]

        return data

    def __process_results(self, df: pd.DataFrame, group_by_columns: list[str]):
        """
        Processes the query results to ensure correct data formatting.
        """
        if df.empty:
            return {}

        for translatable_column in self.__get_translatable_columns(df):
            unique_values = df[translatable_column].dropna().unique()
            translation_map = {value: _(value) for value in unique_values}
            df[translatable_column] = df[translatable_column].replace(translation_map)

        if group_by_columns:
            df[group_by_columns] = df[group_by_columns].fillna("-").astype(str)

        column_types = self.__generate_column_types()
        numeric_columns = [
            column
            for column in column_types.keys()
            if column_types[column] in ["int", "float"] and column in df.columns
        ]

        if numeric_columns:
            df[numeric_columns] = df[numeric_columns].fillna(0)

        non_group_columns = [
            column for column in df.columns if column not in group_by_columns
        ]
        for column, dtype in column_types.items():
            if column in non_group_columns:
                if dtype == "int":
                    df[column] = df[column].astype("Int64")
                elif dtype == "float":
                    df[column] = df[column].astype(float)
                elif dtype == "string":
                    df[column] = df[column].where(pd.notna(df[column]), None)
                    df[column] = df[column].astype(str)
                    df[column] = df[column].replace("None", None)

        if group_by_columns:
            df = df.set_index(group_by_columns)
            return self.__nest_dict(df)

        return df.to_dict(orient="records")

    def __nest_dict(self, df: pd.DataFrame) -> dict:
        """
        Converts a Pandas DataFrame with a multi-index into a nested dictionary.
        """
        data = df.stack(future_stack=True).to_dict()
        nested_dict = {}

        for keys, value in data.items():
            current_level = nested_dict
            *parent_keys, last_key = keys

            for key in parent_keys:
                current_level = current_level.setdefault(key, {})

            current_level[last_key] = value

        return nested_dict

    def __cast_dates_cdf(self, column, granularity: Granularity):
        """
        Casts date-like columns to a string format based on granularity.
        """
        format_map = {
            Granularity.year: "YYYY",
            Granularity.month: "YYYY-MM",
            Granularity.day: "YYYY-MM-DD",
        }
        date_format = format_map.get(granularity, "YYYY-MM-DD")

        return func.to_char(column, date_format).label(column.name)

    def __generate_column_types(self):
        """
        Dynamically generates a dictionary mapping SQLAlchemy column names to their expected types - defaults unknown types to 'float'.
        """
        if self.cast_results_to_string:
            type_mapping = {
                Integer: "string",
                BigInteger: "string",
                Float: "string",
                String: "string",
                Date: "string",
            }
        else:
            type_mapping = {
                Integer: "int",
                BigInteger: "int",
                Float: "float",
                String: "string",
                Date: "string",
            }

        column_types = {}
        for column in class_mapper(self.model).columns:
            sqlalchemy_type = type(column.type)
            column_types[column.key] = type_mapping.get(sqlalchemy_type, "float")

        return column_types

    def __get_translatable_columns(self, df: pd.DataFrame) -> list[str]:
        """
        Returns a list of columns present in the dataframe that are marked as translatable.
        """
        translatable_columns = set(getattr(self.model, "__translate_columns__", []))
        return [column for column in translatable_columns if column in df.columns]
