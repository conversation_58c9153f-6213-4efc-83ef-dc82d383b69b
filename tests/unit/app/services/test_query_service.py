from unittest.mock import patch

import pandas as pd
from sqlalchemy import (
    <PERSON><PERSON><PERSON>ger,
    Column,
    Date,
    Float,
    Integer,
    String,
    func,
    literal_column,
)
from sqlalchemy.orm import class_mapper

from app.enums.column import Column as ColumnEnum
from app.enums.granularity import Granularity
from app.enums.sort import Sort
from app.models.occupancy import Occupancy, OccupancyAndRevenue


class TestQueryService:
    def test_process_results_empty_df(self, query_service):
        df = pd.DataFrame()
        result = query_service._QueryService__process_results(df, group_by_columns=[])
        assert result == {}

    def test_process_results_preserves_none_values(self, query_service):
        df = pd.DataFrame(
            {
                "stay_date": ["2025-03-20", "2025-03-21"],
                "group_profile": [None, None],
                "total_rooms_sold": [5, 3],
                "total_revenue": [1000.0, 750.0],
            }
        )

        result = query_service._QueryService__process_results(
            df, group_by_columns=["stay_date"]
        )
        assert result == {
            "2025-03-20": {
                "group_profile": None,
                "total_rooms_sold": 5,
                "total_revenue": 1000.0,
            },
            "2025-03-21": {
                "group_profile": None,
                "total_rooms_sold": 3,
                "total_revenue": 750.0,
            },
        }

    def test_process_results_numeric_casting(self, query_service):
        df = pd.DataFrame(
            {
                "stay_date": ["2025-03-20", "2025-03-21"],
                "capacity_count": [10, None],
                "total_room_rate": [150.75, 120.0],
            }
        )
        result = query_service._QueryService__process_results(
            df, group_by_columns=["stay_date"]
        )

        assert result == {
            "2025-03-20": {"capacity_count": 10.0, "total_room_rate": 150.75},
            "2025-03-21": {"capacity_count": 0.0, "total_room_rate": 120.0},
        }

    def test_process_results_group_by_nesting(self, query_service):
        df = pd.DataFrame(
            {
                "stay_date": ["2025-03-20", "2025-03-20", "2025-03-21"],
                "group_profile": ["VIP", "Regular", None],
                "total_rooms_sold": [5, 2, 3],
            }
        )

        result = query_service._QueryService__process_results(
            df, group_by_columns=["stay_date", "group_profile"]
        )
        assert result == {
            "2025-03-20": {
                "VIP": {"total_rooms_sold": 5},
                "Regular": {"total_rooms_sold": 2},
            },
            "2025-03-21": {"-": {"total_rooms_sold": 3}},
        }

    def test_process_results_group_by_none_string(self, query_service):
        df = pd.DataFrame(
            {
                "stay_date": ["2025-03-20", "2025-03-20", "2025-03-21"],
                "group_profile": ["VIP", "Regular", "None"],
                "total_rooms_sold": [5, 2, 3],
            }
        )

        result = query_service._QueryService__process_results(
            df, group_by_columns=["stay_date", "group_profile"]
        )
        assert result == {
            "2025-03-20": {
                "VIP": {"total_rooms_sold": 5},
                "Regular": {"total_rooms_sold": 2},
            },
            "2025-03-21": {"None": {"total_rooms_sold": 3}},
        }

    def test_process_results_fills_missing_numeric_values(self, query_service):
        df = pd.DataFrame(
            {
                "stay_date": ["2025-03-20", "2025-03-21"],
                "occupancy": [None, 85.5],
                "total_revenue": [None, 750.0],
            }
        )

        result = query_service._QueryService__process_results(
            df, group_by_columns=["stay_date"]
        )
        assert result == {
            "2025-03-20": {"occupancy": 0.0, "total_revenue": 0.0},
            "2025-03-21": {"occupancy": 85.5, "total_revenue": 750.0},
        }

    def test_generate_column_types(self, query_service):
        expected_type_mapping = {
            Integer: "int",
            BigInteger: "int",
            Float: "float",
            String: "string",
            Date: "string",
        }
        column_types = query_service._QueryService__generate_column_types()

        for column in class_mapper(OccupancyAndRevenue).columns:
            sqlalchemy_type = type(column.type)
            expected_type = expected_type_mapping.get(sqlalchemy_type, "float")

            assert column_types[column.key] == expected_type

    def test_generate_column_types_defaults_to_float(self, query_service):
        class FakeColumn:
            type = object

        unknown_column = FakeColumn()
        column_types = query_service._QueryService__generate_column_types()
        column_types[
            "unknown_column"
        ] = query_service._QueryService__generate_column_types().get(
            unknown_column.type, "float"
        )

        assert column_types["unknown_column"] == "float"

    def test_cast_dates_cdf(self, query_service):
        column = Column("stay_date", Date)
        result_year = query_service._QueryService__cast_dates_cdf(
            column, Granularity.year
        )
        result_month = query_service._QueryService__cast_dates_cdf(
            column, Granularity.month
        )
        result_day = query_service._QueryService__cast_dates_cdf(
            column, Granularity.day
        )
        result_default = query_service._QueryService__cast_dates_cdf(column, None)

        assert str(result_year) == str(func.to_char(column, "YYYY").label("stay_date"))
        assert str(result_month) == str(
            func.to_char(column, "YYYY-MM").label("stay_date")
        )
        assert str(result_day) == str(
            func.to_char(column, "YYYY-MM-DD").label("stay_date")
        )
        assert str(result_default) == str(
            func.to_char(column, "YYYY-MM-DD").label("stay_date")
        )

    def test_extract_column_names(self, query_service):
        input_columns = [ColumnEnum.total_revenue, "occupancy", ColumnEnum.room_id]
        expected_output = ["total_revenue", "occupancy", "room_id"]

        assert (
            query_service._QueryService__extract_column_names(input_columns)
            == expected_output
        )

    def test_prepare_group_by_columns(self, query_service):
        group_by_columns = ["stay_date", "property_id"]
        (
            selected_columns,
            group_by_expressions,
        ) = query_service._QueryService__prepare_group_by_columns(
            group_by_columns, Granularity.day, group_by_columns
        )
        assert len(group_by_columns) == len(group_by_expressions)

        stay_date_col = next(
            selected_column
            for selected_column in selected_columns
            if str(selected_column).startswith("to_char")
        )

        assert "to_char(occupancy.occupancy.stay_date, :to_char_1)" in str(
            stay_date_col
        )

        # Check for property_id column - handle both traditional and optimized formats
        selected_strs = [str(col) for col in selected_columns]
        property_id_found = any(
            "occupancy.occupancy.property_id" in col_str
            or "property_id_placeholder" in col_str
            or "OccupancyAndRevenue.property_id" in col_str
            for col_str in selected_strs
        )
        assert (
            property_id_found
        ), f"property_id not found in selected columns: {selected_strs}"

    def test_generate_aggregation_columns(self, query_service):
        aggregation_columns = (
            query_service._QueryService__generate_aggregation_columns()
        )
        expected_columns = set(OccupancyAndRevenue.__aggregate_columns__)

        assert expected_columns.issubset(set(aggregation_columns.keys()))

        for _col_name, col_expr in aggregation_columns.items():
            assert "sum" in str(col_expr).lower() or "cast" in str(col_expr).lower()

    def test_generate_non_aggregation_columns(self, query_service):
        non_aggregation_columns = (
            query_service._QueryService__generate_non_aggregation_columns()
        )

        for col_name in OccupancyAndRevenue.__aggregate_columns__:
            assert col_name not in non_aggregation_columns

        expected_columns = {
            col.key
            for col in class_mapper(OccupancyAndRevenue).columns
            if col.key not in OccupancyAndRevenue.__aggregate_columns__
        }
        assert set(non_aggregation_columns.keys()) == expected_columns

    def test_generate_sort_columns(self, query_service):
        sorts = [Sort.adr_asc, Sort.revpar_desc]
        groups = ["reservation_source"]
        sort_expressions = query_service._QueryService__generate_sort_columns(
            sorts, groups
        )

        assert len(sort_expressions) == 2
        assert str(sort_expressions[0]) == str(literal_column("adr").asc())
        assert str(sort_expressions[1]) == str(literal_column("revpar").desc())

        empty_sorts = query_service._QueryService__generate_sort_columns([], groups)
        assert len(empty_sorts) == 1
        assert str(empty_sorts[0]) == str(
            literal_column("reservation_source").asc().nullsfirst()
        )

    def test_generate_sort_columns_groups(self, query_service):
        sorts = [Sort.adr_desc]
        groups = ["reservation_source"]
        result = query_service._QueryService__generate_sort_columns(sorts, groups)

        assert len(result) == 1
        assert str(result[0]) == str(literal_column("adr").desc())

    def test_determine_model_empty_request(self, query_service):
        model = query_service._QueryService__determine_model([], None, None)
        assert model == Occupancy

    def test_determine_model_occupancy_only(self, query_service):
        """Test that Occupancy model is returned when only using occupancy columns."""
        occupancy_columns = [
            "occupancy",
            "total_rooms_sold",
            "capacity_count",
            "organization_id",
        ]
        model = query_service._QueryService__determine_model(
            occupancy_columns, ["stay_date", "property_id"], []
        )
        assert model == Occupancy

    def test_determine_model_revenue_columns(self, query_service):
        """Test that OccupancyAndRevenue model is returned when revenue columns are requested."""
        columns = ["total_revenue", "occupancy"]
        model = query_service._QueryService__determine_model(columns, ["stay_date"], [])
        assert model == OccupancyAndRevenue

    def test_determine_model_revenue_group_by(self, query_service):
        """Test that OccupancyAndRevenue model is returned when grouping by revenue column."""
        columns = ["occupancy", "total_rooms_sold"]
        model = query_service._QueryService__determine_model(
            columns, ["reservation_source"], []
        )
        assert model == OccupancyAndRevenue

    def test_determine_model_revenue_sort(self, query_service):
        """Test that OccupancyAndRevenue model is returned when sorting by revenue column."""
        columns = ["occupancy", "total_rooms_sold"]
        sorts = [Sort.revpar_desc, Sort.adr_asc]
        model = query_service._QueryService__determine_model(
            columns, ["stay_date", "property_id"], sorts
        )
        assert model == OccupancyAndRevenue

    def test_determine_model_mixed_columns_sorts(self, query_service):
        """Test with a mix of columns, group_bys, and sorts."""
        occupancy_columns = ["total_rooms_sold", "capacity_count"]
        group_bys = ["stay_date", "property_id"]
        sorts = [Sort.occupancy_desc]  # occupancy is available in both models

        model = query_service._QueryService__determine_model(
            occupancy_columns, group_bys, sorts
        )
        assert model == Occupancy

        # Adding just one revenue-specific column should switch to OccupancyAndRevenue
        model = query_service._QueryService__determine_model(
            occupancy_columns + ["total_revenue"], group_bys, sorts
        )
        assert model == OccupancyAndRevenue

    def test_prepare_group_by_columns_includes_dependent_column(self, query_service):
        group_by_columns = ["room_type"]
        requested_columns = ["room_type_id"]

        (
            selected_columns,
            group_by_expressions,
        ) = query_service._QueryService__prepare_group_by_columns(
            group_by_columns, Granularity.day, group_by_columns + requested_columns
        )

        selected_strs = [str(col) for col in selected_columns]
        assert any("room_type_id" in col for col in selected_strs)
        assert any("room_type" in col for col in selected_strs)

        group_by_strs = [str(expr) for expr in group_by_expressions]
        assert any("room_type_id" in expr for expr in group_by_strs)
        assert any("room_type" in expr for expr in group_by_strs)

    def test_should_use_optimized_queries_true(self, query_service):
        """Test that optimized queries are used when LaunchDarkly flag is enabled and model is Occupancy."""
        query_service.model = Occupancy

        # Mock the entire LaunchDarklyService class to bypass cache
        with patch("app.services.query.LaunchDarklyService") as mock_service:
            mock_service.has_feature_flag.return_value = True

            assert query_service._QueryService__should_use_optimized_queries() is True

    def test_should_use_optimized_queries_false_disabled(self, query_service):
        """Test that optimized queries are not used when LaunchDarkly flag is disabled."""
        query_service.model = Occupancy

        # Mock the entire LaunchDarklyService class to bypass cache
        with patch("app.services.query.LaunchDarklyService") as mock_service:
            mock_service.has_feature_flag.return_value = False

            assert query_service._QueryService__should_use_optimized_queries() is False

    def test_should_use_optimized_queries_false_wrong_model(self, query_service):
        """Test that optimized queries are not used with OccupancyAndRevenue model."""
        query_service.model = OccupancyAndRevenue

        # Should return False regardless of LaunchDarkly flag because model is wrong
        assert query_service._QueryService__should_use_optimized_queries() is False

    def test_generate_optimized_aggregation_columns(self, query_service):
        """Test that optimized aggregation columns are generated correctly."""
        query_service.model = Occupancy

        with patch("app.services.query.LaunchDarklyService") as mock_service:
            mock_service.has_feature_flag.return_value = True

            aggregation_columns = (
                query_service._QueryService__generate_aggregation_columns()
            )

            expected_columns = {
                "occupancy",
                "capacity_count",
                "total_rooms_sold",
                "total_rooms_available",
                "blocked_rooms_count",
                "out_of_service_count",
                "guest_count",
                "adults_count",
                "children_count",
            }

            assert set(aggregation_columns.keys()) == expected_columns

            # Check that all columns have placeholder labels
            for column_name, column_expr in aggregation_columns.items():
                assert hasattr(column_expr, "name")
                assert column_expr.name == column_name

    def test_generate_optimized_non_aggregation_columns(self, query_service):
        """Test that optimized non-aggregation columns are generated correctly."""
        query_service.model = Occupancy

        with patch("app.services.query.LaunchDarklyService") as mock_service:
            mock_service.has_feature_flag.return_value = True

            non_aggregation_columns = (
                query_service._QueryService__generate_non_aggregation_columns()
            )

            expected_columns = {
                "stay_date",
                "organization_id",
                "property_id",
                "room_type_id",
                "room_type",
                "group_profile_code",
            }

            assert set(non_aggregation_columns.keys()) == expected_columns

            # Check that all columns have placeholder labels
            for column_name, column_expr in non_aggregation_columns.items():
                assert hasattr(column_expr, "name")
                assert column_expr.name == column_name
